extends CharacterBody3D
class_name PlayerController

# Movement parameters
@export_group("Movement")
@export var walk_speed: float = 5.0
@export var run_speed: float = 8.0
@export var acceleration: float = 10.0
@export var deceleration: float = 15.0
@export var air_control: float = 0.3
@export var jump_height: float = 2.0
@export var gravity_multiplier: float = 3.0

# Advanced Movement parameters
@export_group("Advanced Movement")
@export var bunny_hop_boost: float = 1.5  # Speed multiplier for bunny hopping
@export var air_strafe_acceleration: float = 15.0  # Air strafing acceleration
@export var max_air_speed: float = 12.0  # Maximum air speed for strafing
@export var jump_buffer_time: float = 0.2  # Time in seconds to buffer jump input
@export var coyote_time: float = 0.15  # Time in seconds player can jump after leaving a ledge
@export var double_jump_height: float = 1.5  # Height multiplier for double jump
@export var wall_run_speed: float = 7.0  # Speed while wall running
@export var wall_run_gravity: float = 0.5  # Gravity multiplier while wall running
@export var wall_jump_height: float = 1.2  # Height multiplier for wall jump
@export var wall_jump_push: float = 6.0  # Horizontal push when wall jumping
@export var knockback_cooldown: float = 0.5  # Cooldown between knockbacks
@export var knockback_force: float = 10.0  # Force of knockback

# Camera parameters
@export_group("Camera")
@export var mouse_sensitivity: float = 0.002
@export var vertical_angle_limit: float = 90.0
@export var head_bob_enabled: bool = true
@export var head_bob_amount: float = 0.05
@export var head_bob_speed: float = 10.0
@export var camera_tilt_angle: float = 5.0  # Maximum camera tilt angle in degrees
@export var camera_tilt_speed: float = 5.0  # Speed of camera tilt transition

# Crouch parameters
@export_group("Crouch")
@export var crouch_speed: float = 3.0
@export var crouch_height: float = 1.0
@export var stand_height: float = 1.8
@export var crouch_transition_speed: float = 10.0

# Slide parameters
@export_group("Slide")
@export var slide_speed: float = 10.0
@export var slide_time: float = 1.0
@export var slide_cooldown: float = 1.0

# Dash parameters
@export_group("Dash")
@export var dash_speed: float = 20.0
@export var dash_time: float = 0.2
@export var dash_cooldown: float = 1.5

# State variables
var gravity: float = ProjectSettings.get_setting("physics/3d/default_gravity") * gravity_multiplier
var is_running: bool = false
var is_crouching: bool = false
var is_sliding: bool = false
var is_dashing: bool = false
var is_wall_running: bool = false
var is_aiming: bool = false
var can_slide: bool = true
var can_dash: bool = true
var can_double_jump: bool = true
var can_knockback: bool = true
var slide_timer: float = 0.0
var slide_cooldown_timer: float = 0.0
var dash_timer: float = 0.0
var dash_cooldown_timer: float = 0.0
var jump_buffer_timer: float = 0.0
var coyote_timer: float = 0.0
var knockback_timer: float = 0.0
var last_position: Vector3 = Vector3.ZERO
var velocity_y: float = 0.0
var current_speed: float = 0.0
var head_bob_cycle: float = 0.0
var target_height: float = stand_height
var camera_tilt: float = 0.0  # Current camera tilt in degrees
var wall_normal: Vector3 = Vector3.ZERO  # Normal of the wall we're running on
var last_floor_time: float = 0.0  # Time since player was last on floor
var last_jump_time: float = 0.0  # Time since player last jumped
var bunny_hop_window: float = 0.2  # Time window for bunny hop after landing

# Node references
@onready var camera_mount: Node3D = $CameraMount
@onready var camera: Camera3D = $CameraMount/Camera
@onready var collision_shape: CollisionShape3D = $CollisionShape
@onready var capsule_shape: CapsuleShape3D = $CollisionShape.shape
@onready var weapon_manager: Node3D = $CameraMount/Camera/WeaponManager
@onready var health_system: Node = $HealthSystem
@onready var footstep_audio: AudioStreamPlayer3D = $FootstepAudio
@onready var jump_audio: AudioStreamPlayer3D = $JumpAudio
@onready var weapon_customization: CanvasLayer

# Wall detection raycasts
@onready var wall_check_left: RayCast3D
@onready var wall_check_right: RayCast3D
@onready var wall_check_forward: RayCast3D

# Input variables
var input_dir: Vector2 = Vector2.ZERO
var look_dir: Vector2 = Vector2.ZERO  # Used in _rotate_camera
var wish_dir: Vector3 = Vector3.ZERO
var last_mouse_position: Vector2 = Vector2.ZERO  # Used for mouse look

# Signals
signal player_jumped
signal player_landed
signal player_crouched(is_crouched)
signal player_started_running(is_running)
signal player_took_damage(amount, health)
signal player_died
signal player_wall_running(is_wall_running)
signal player_double_jumped
signal player_aiming(is_aiming)

func _ready():
	# Initialize
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	last_position = global_position

	# Create wall detection raycasts
	_setup_wall_raycasts()

	# Setup weapon customization
	_setup_weapon_customization()

	# Connect signals
	if health_system:
		health_system.health_changed.connect(_on_health_changed)
		health_system.died.connect(_on_died)

func _setup_wall_raycasts():
	# Create left wall check
	wall_check_left = RayCast3D.new()
	add_child(wall_check_left)
	wall_check_left.position = Vector3(0, 1, 0)  # Position at player's center
	wall_check_left.target_position = Vector3(-1.0, 0, 0)  # Check 1 meter to the left
	wall_check_left.collision_mask = 1  # Set to collide with world geometry

	# Create right wall check
	wall_check_right = RayCast3D.new()
	add_child(wall_check_right)
	wall_check_right.position = Vector3(0, 1, 0)
	wall_check_right.target_position = Vector3(1.0, 0, 0)  # Check 1 meter to the right
	wall_check_right.collision_mask = 1

	# Create forward wall check
	wall_check_forward = RayCast3D.new()
	add_child(wall_check_forward)
	wall_check_forward.position = Vector3(0, 1, 0)
	wall_check_forward.target_position = Vector3(0, 0, -1.0)  # Check 1 meter forward
	wall_check_forward.collision_mask = 1

func _input(event):
	# Handle mouse look
	if event is InputEventMouseMotion and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
		var mouse_delta = event.relative * mouse_sensitivity
		_rotate_camera(mouse_delta)

func _physics_process(delta):
	# Handle input
	_handle_input()

	# Update timers
	_update_timers(delta)

	# Check for wall running
	_check_wall_running()

	# Apply gravity based on state
	if is_wall_running:
		velocity.y -= gravity * wall_run_gravity * delta
	elif not is_on_floor():
		velocity.y -= gravity * delta

		# Start coyote time when leaving a platform
		if coyote_timer <= 0 and last_floor_time < coyote_time:
			coyote_timer = coyote_time

	# Handle movement states
	if is_dashing:
		_handle_dash(delta)
	elif is_sliding:
		_handle_slide(delta)
	elif is_wall_running:
		_handle_wall_running(delta)
	else:
		_handle_normal_movement(delta)

	# Apply camera tilt
	_update_camera_tilt(delta)

	# Apply movement
	move_and_slide()

	# Handle head bob
	if head_bob_enabled and is_on_floor() and input_dir.length() > 0.1:
		_apply_head_bob(delta)

	# Update height for crouching
	_update_player_height(delta)

	# Check if just landed
	if is_on_floor():
		if velocity.y < -5.0:
			emit_signal("player_landed")

		# Reset double jump when landing
		can_double_jump = true

		# Track time for bunny hopping
		last_floor_time = 0

		# Check for bunny hop opportunity
		if Time.get_ticks_msec() / 1000.0 - last_jump_time < bunny_hop_window:
			# Player jumped recently, apply bunny hop boost
			current_speed *= bunny_hop_boost
	else:
		# Update time since last on floor
		last_floor_time += delta

func _handle_input():
	# Get movement input
	input_dir = Input.get_vector("moveLeft", "moveRight", "moveForward", "moveBackward")

	# Convert input to 3D direction relative to camera orientation
	var input_dir_3d = Vector3(input_dir.x, 0, input_dir.y).normalized()
	wish_dir = (camera_mount.global_transform.basis * input_dir_3d).normalized()
	wish_dir.y = 0

	# Handle jump with buffer and coyote time
	if Input.is_action_just_pressed("jump"):
		if (is_on_floor() or coyote_timer > 0) and !is_sliding:
			# Normal jump
			_perform_jump()
		elif is_wall_running and Input.is_action_pressed("jump"):
			# Wall jump
			_perform_wall_jump()
		elif !is_on_floor() and can_double_jump:
			# Double jump
			_perform_double_jump()
		else:
			# Buffer the jump input
			jump_buffer_timer = jump_buffer_time

	# Process buffered jump if we landed
	if jump_buffer_timer > 0 and is_on_floor() and !is_sliding:
		_perform_jump()
		jump_buffer_timer = 0

	# Handle run
	var was_running = is_running
	is_running = Input.is_action_pressed("run") and input_dir.length() > 0.1 and !is_crouching
	if was_running != is_running:
		emit_signal("player_started_running", is_running)

	# Handle crouch
	if Input.is_action_just_pressed("crouch | slide"):
		if is_on_floor() and input_dir.length() > 0.5 and is_running and can_slide:
			_start_slide()
		else:
			is_crouching = !is_crouching
			target_height = crouch_height if is_crouching else stand_height
			emit_signal("player_crouched", is_crouching)

	# Handle dash
	if Input.is_action_just_pressed("dash") and can_dash:
		_start_dash()

	# Handle camera tilt (lean)
	if Input.is_action_pressed("leanLeft"):
		camera_tilt = -camera_tilt_angle
	elif Input.is_action_pressed("leanRight"):
		camera_tilt = camera_tilt_angle
	else:
		camera_tilt = 0

	# Handle aiming
	var was_aiming = is_aiming
	is_aiming = Input.is_action_pressed("secondary_fire")
	if was_aiming != is_aiming:
		emit_signal("player_aiming", is_aiming)

	# Handle knockback
	if Input.is_action_just_pressed("useKnockback") and can_knockback:
		_apply_knockback()

	# Handle weapon customization
	if Input.is_action_just_pressed("weapon_customization"):
		_toggle_weapon_customization()

func _handle_normal_movement(delta):
	var target_speed = walk_speed
	if is_running:
		target_speed = run_speed
	elif is_crouching:
		target_speed = crouch_speed

	var accel = acceleration
	if !is_on_floor():
		# Air strafing mechanics
		_handle_air_strafing(delta, target_speed)
	elif input_dir.length() < 0.1:
		accel = deceleration
		# Calculate horizontal velocity
		var horizontal_velocity = Vector3(velocity.x, 0, velocity.z)
		var target_velocity = wish_dir * target_speed
		horizontal_velocity = horizontal_velocity.lerp(target_velocity, accel * delta)
		velocity.x = horizontal_velocity.x
		velocity.z = horizontal_velocity.z
	else:
		# Ground movement
		var horizontal_velocity = Vector3(velocity.x, 0, velocity.z)
		var target_velocity = wish_dir * target_speed
		horizontal_velocity = horizontal_velocity.lerp(target_velocity, accel * delta)
		velocity.x = horizontal_velocity.x
		velocity.z = horizontal_velocity.z

	# Update current speed for effects
	var horizontal_velocity_final = Vector3(velocity.x, 0, velocity.z)
	current_speed = horizontal_velocity_final.length()

func _handle_slide(delta):
	slide_timer -= delta

	if slide_timer <= 0 or !is_on_floor():
		_end_slide()
		return

	# Maintain slide direction but gradually slow down
	var slide_factor = slide_timer / slide_time
	var current_slide_speed = lerp(walk_speed, slide_speed, slide_factor)

	velocity.x = wish_dir.x * current_slide_speed
	velocity.z = wish_dir.z * current_slide_speed

func _handle_dash(delta):
	dash_timer -= delta

	if dash_timer <= 0:
		_end_dash()
		return

	velocity.x = wish_dir.x * dash_speed
	velocity.z = wish_dir.z * dash_speed

func _start_slide():
	is_sliding = true
	is_crouching = true
	target_height = crouch_height
	slide_timer = slide_time
	can_slide = false
	slide_cooldown_timer = slide_cooldown
	emit_signal("player_crouched", true)

func _end_slide():
	is_sliding = false
	# Stay crouched after sliding
	is_crouching = true

func _start_dash():
	is_dashing = true
	dash_timer = dash_time
	can_dash = false
	dash_cooldown_timer = dash_cooldown

func _end_dash():
	is_dashing = false

func _update_timers(delta):
	# Slide cooldown
	if !can_slide:
		slide_cooldown_timer -= delta
		if slide_cooldown_timer <= 0:
			can_slide = true

	# Dash cooldown
	if !can_dash:
		dash_cooldown_timer -= delta
		if dash_cooldown_timer <= 0:
			can_dash = true

	# Jump buffer timer
	if jump_buffer_timer > 0:
		jump_buffer_timer -= delta

	# Coyote time timer
	if coyote_timer > 0:
		coyote_timer -= delta

	# Knockback cooldown
	if !can_knockback:
		knockback_timer -= delta
		if knockback_timer <= 0:
			can_knockback = true

func _rotate_camera(mouse_delta: Vector2):
	# Rotate player horizontally
	rotate_y(-mouse_delta.x)

	# Rotate camera vertically
	camera_mount.rotate_x(-mouse_delta.y)
	camera_mount.rotation.x = clamp(camera_mount.rotation.x,
								   deg_to_rad(-vertical_angle_limit),
								   deg_to_rad(vertical_angle_limit))

func _apply_head_bob(delta):
	head_bob_cycle += delta * head_bob_speed * current_speed / walk_speed
	var bob_offset = sin(head_bob_cycle) * head_bob_amount
	camera.position.y = bob_offset

func _update_player_height(delta):
	var current_height = capsule_shape.height
	var new_height = lerp(current_height, target_height, crouch_transition_speed * delta)

	capsule_shape.height = new_height
	collision_shape.position.y = new_height / 2

	# Adjust camera position based on crouch state
	# Calculate target camera height: start from the initial camera height (1.9) and adjust based on crouch
	var initial_camera_height = 1.9
	var target_camera_y = initial_camera_height - (stand_height - new_height)
	camera_mount.position.y = lerp(camera_mount.position.y, target_camera_y, crouch_transition_speed * delta)

func take_damage(amount: float, _source: Vector3 = Vector3.ZERO):
	if health_system:
		health_system.take_damage(amount)

func heal(amount: float):
	if health_system:
		health_system.heal(amount)

func _on_health_changed(new_health: float, damage_taken: float):
	emit_signal("player_took_damage", damage_taken, new_health)

func _on_died():
	emit_signal("player_died")
	# Handle death (disable controls, show death screen, etc.)

func get_weapon_manager() -> Node:
	return weapon_manager

# Advanced movement functions
func _perform_jump():
	velocity.y = sqrt(2.0 * gravity * jump_height)
	emit_signal("player_jumped")
	if jump_audio:
		jump_audio.play()

	# Record jump time for bunny hopping
	last_jump_time = Time.get_ticks_msec() / 1000.0

func _perform_double_jump():
	velocity.y = sqrt(2.0 * gravity * jump_height * double_jump_height)
	can_double_jump = false
	emit_signal("player_double_jumped")
	if jump_audio:
		jump_audio.play()

func _perform_wall_jump():
	# Jump away from the wall
	velocity.y = sqrt(2.0 * gravity * jump_height * wall_jump_height)

	# Push away from wall
	var push_dir = wall_normal
	push_dir.y = 0
	push_dir = push_dir.normalized()

	velocity.x = push_dir.x * wall_jump_push
	velocity.z = push_dir.z * wall_jump_push

	# End wall running
	is_wall_running = false

	emit_signal("player_jumped")
	if jump_audio:
		jump_audio.play()

func _check_wall_running():
	# Only check for walls if we're in the air and holding jump
	if is_on_floor() or !Input.is_action_pressed("jump"):
		if is_wall_running:
			is_wall_running = false
			emit_signal("player_wall_running", false)
		return

	# Update raycast directions based on player orientation
	wall_check_left.target_position = -transform.basis.x
	wall_check_right.target_position = transform.basis.x
	wall_check_forward.target_position = -transform.basis.z

	# Check for walls
	var found_wall = false
	wall_normal = Vector3.ZERO

	if wall_check_left.is_colliding():
		wall_normal = wall_check_left.get_collision_normal()
		found_wall = true
	elif wall_check_right.is_colliding():
		wall_normal = wall_check_right.get_collision_normal()
		found_wall = true
	elif wall_check_forward.is_colliding():
		wall_normal = wall_check_forward.get_collision_normal()
		found_wall = true

	# Start or stop wall running
	if found_wall and !is_wall_running:
		is_wall_running = true
		emit_signal("player_wall_running", true)
	elif !found_wall and is_wall_running:
		is_wall_running = false
		emit_signal("player_wall_running", false)

func _handle_wall_running(_delta):
	# Move along the wall
	var wall_dir = wall_normal.cross(Vector3.UP).normalized()

	# Determine direction based on input
	var along_wall_speed = 0.0

	if wish_dir.dot(wall_dir) > 0:
		along_wall_speed = wall_run_speed
	elif wish_dir.dot(-wall_dir) > 0:
		along_wall_speed = -wall_run_speed

	velocity.x = wall_dir.x * along_wall_speed
	velocity.z = wall_dir.z * along_wall_speed

func _update_camera_tilt(delta):
	# Apply camera tilt for leaning and wall running
	var target_tilt = camera_tilt

	# Add wall running tilt
	if is_wall_running:
		var wall_tilt_dir = wall_normal.cross(Vector3.UP).normalized()
		var right_dot = wall_tilt_dir.dot(transform.basis.x)

		if right_dot > 0:
			target_tilt -= camera_tilt_angle  # Tilt left when running on right wall
		else:
			target_tilt += camera_tilt_angle  # Tilt right when running on left wall

	# Smoothly apply tilt
	var current_tilt = camera_mount.rotation.z
	var new_tilt = lerp(current_tilt, deg_to_rad(target_tilt), camera_tilt_speed * delta)
	camera_mount.rotation.z = new_tilt

func _apply_knockback():
	# Apply knockback in the direction player is looking
	var knockback_dir = -camera_mount.global_transform.basis.z.normalized()
	knockback_dir.y = 0

	velocity += knockback_dir * knockback_force

	# Start cooldown
	can_knockback = false
	knockback_timer = knockback_cooldown

func _setup_weapon_customization():
	# Try to find weapon customization in the scene
	var scene_root = get_tree().current_scene
	if scene_root:
		weapon_customization = _find_weapon_customization_recursive(scene_root)

	# If not found, create one
	if not weapon_customization:
		var customization_scene = preload("res://Core/UI/WeaponCustomization.tscn")
		weapon_customization = customization_scene.instantiate()
		get_tree().current_scene.add_child.call_deferred(weapon_customization)

func _find_weapon_customization_recursive(node: Node) -> CanvasLayer:
	if node.get_script() and node.get_script().get_global_name() == "WeaponCustomization":
		return node

	for child in node.get_children():
		var result = _find_weapon_customization_recursive(child)
		if result:
			return result

	return null

func _toggle_weapon_customization():
	if weapon_customization and is_instance_valid(weapon_customization):
		# Check if the customization container exists and is ready
		if weapon_customization.has_method("hide_customization") and weapon_customization.has_method("show_customization"):
			var container = weapon_customization.get_node_or_null("CustomizationContainer")
			if container and container.visible:
				weapon_customization.hide_customization()
			else:
				weapon_customization.show_customization()
		else:
			print("ERROR: WeaponCustomization methods not available")
	else:
		print("ERROR: WeaponCustomization not available")

func _handle_air_strafing(delta: float, target_speed: float):
	# Air strafing allows for more precise air control and speed building
	var horizontal_velocity = Vector3(velocity.x, 0, velocity.z)
	var horizontal_speed = horizontal_velocity.length()

	# Only apply air strafing if we have input
	if input_dir.length() > 0.1:
		var strafe_dir = wish_dir
		var dot_product = horizontal_velocity.normalized().dot(strafe_dir)

		# If we're moving in a different direction than current velocity, allow acceleration
		if dot_product < 0.9 or horizontal_speed < max_air_speed:
			var acceleration_amount = air_strafe_acceleration * delta

			# Scale acceleration based on how different the direction is
			if dot_product > 0:
				acceleration_amount *= (1.0 - dot_product)

			var new_velocity = horizontal_velocity + strafe_dir * acceleration_amount

			# Limit maximum air speed
			if new_velocity.length() > max_air_speed:
				new_velocity = new_velocity.normalized() * max_air_speed

			velocity.x = new_velocity.x
			velocity.z = new_velocity.z
		else:
			# Apply normal air control if we're at max speed
			var target_velocity = wish_dir * target_speed
			horizontal_velocity = horizontal_velocity.lerp(target_velocity, air_control * delta)
			velocity.x = horizontal_velocity.x
			velocity.z = horizontal_velocity.z
	else:
		# No input, maintain current velocity with slight air resistance
		horizontal_velocity *= (1.0 - 0.1 * delta)
		velocity.x = horizontal_velocity.x
		velocity.z = horizontal_velocity.z
