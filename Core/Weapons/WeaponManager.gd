extends Node3D
class_name WeaponManager

# Weapon slots
@export var max_weapons: int = 5
@export var weapon_position: Node3D
@export var weapon_resources: Array[PackedScene] = []

# Weapon switching
@export var switch_time: float = 0.3
var is_switching: bool = false
var current_weapon_index: int = -1
var weapons: Array = []

# HUD reference
@export var weapon_hud: Control

# Signals
signal weapon_switched(weapon)
signal weapon_fired(weapon)
signal ammo_changed(weapon, current, reserve)
signal weapon_added(weapon, index)
# signal weapon_removed(weapon, index) # Uncomment when needed
signal no_ammo(weapon)

func _ready():
	print("WeaponManager initialized")

	# Set process mode to ensure weapon manager works during pause
	process_mode = Node.PROCESS_MODE_PAUSABLE

	# Initialize weapons array
	weapons.resize(max_weapons)

	# Connect input events
	set_process_input(true)

	# Load initial weapons if specified
	for i in range(min(weapon_resources.size(), max_weapons)):
		add_weapon_from_resource(weapon_resources[i])

	# Switch to first weapon if available
	if weapons.size() > 0 and current_weapon_index == -1:
		for i in range(weapons.size()):
			if weapons[i] != null:
				switch_to_weapon(i)
				break

func _input(event):
	# Only handle specific weapon input events
	var handled = false

	# Weapon switching with number keys
	for i in range(1, min(10, max_weapons + 1)):
		if event.is_action_pressed("weapon_" + str(i)):
			switch_to_weapon(i - 1)
			handled = true
			break

	# Weapon switching with mouse wheel
	if !handled:
		if event.is_action_pressed("weapon_next"):
			next_weapon()
			handled = true
		elif event.is_action_pressed("weapon_prev"):
			previous_weapon()
			handled = true

	# Weapon firing
	if !handled:
		if event.is_action_pressed("primary_fire"):
			primary_fire()
			handled = true
		elif event.is_action_pressed("secondary_fire"):
			secondary_fire()
			handled = true

	# Reload
	if !handled and event.is_action_pressed("reload"):
		reload_weapon()
		handled = true

func _process(_delta):
	# Handle automatic firing
	if Input.is_action_pressed("primary_fire"):
		if current_weapon_index != -1 and weapons[current_weapon_index] != null:
			if weapons[current_weapon_index].auto_fire:
				primary_fire()

func add_weapon(weapon) -> bool:
	# Validate weapon object
	if weapon == null:
		print("ERROR: Cannot add null weapon")
		return false

	# Check if weapon has required properties
	if not weapon.has_method("get") or not "weapon_name" in weapon:
		print("ERROR: Weapon object missing weapon_name property")
		return false

	# Special handling for PP8 dual-wield weapons
	if weapon.weapon_name == "PP8":
		return _handle_pp8_pickup(weapon)

	# Check if we already have this weapon type
	for i in range(weapons.size()):
		if weapons[i] != null and "weapon_name" in weapons[i] and weapons[i].weapon_name == weapon.weapon_name:
			# Add ammo instead of adding duplicate weapon
			var ammo_to_add = weapon.reserve_ammo + weapon.current_ammo
			var ammo_added = weapons[i].add_ammo(ammo_to_add)
			print("WeaponManager: Added " + str(ammo_added) + " ammo to existing " + weapon.weapon_name)
			emit_signal("ammo_changed", weapons[i], weapons[i].current_ammo, weapons[i].reserve_ammo)
			weapon.queue_free()
			return true  # Return true since we successfully added ammo

	# Find an empty slot
	var slot = -1
	for i in range(weapons.size()):
		if weapons[i] == null:
			slot = i
			break

	if slot == -1:
		return false  # No empty slots

	# Add weapon to slot
	weapons[slot] = weapon
	weapon.visible = false

	# Connect signals
	weapon.weapon_fired.connect(_on_weapon_fired)
	weapon.ammo_changed.connect(_on_ammo_changed)
	weapon.no_ammo.connect(_on_no_ammo)

	emit_signal("weapon_added", weapon, slot)

	# Auto-switch to new weapon if we don't have one equipped
	if current_weapon_index == -1:
		switch_to_weapon(slot)  # Fire and forget

	return true

func add_weapon_from_resource(weapon_resource: PackedScene) -> bool:
	if weapon_resource == null:
		print("ERROR: Weapon resource is null")
		return false

	print("WeaponManager: Instantiating weapon from resource: ", weapon_resource)

	# Instantiate the weapon from the resource
	var weapon_instance = weapon_resource.instantiate()
	if weapon_instance == null:
		print("ERROR: Failed to instantiate weapon from resource")
		return false

	# Wait a frame to ensure the weapon is fully initialized
	await get_tree().process_frame

	# Validate weapon instance has required properties
	var weapon_name_str = "Unknown"
	if "weapon_name" in weapon_instance:
		weapon_name_str = weapon_instance.weapon_name
	elif weapon_instance.has_method("get_weapon_name"):
		weapon_name_str = weapon_instance.get_weapon_name()

	print("WeaponManager: Successfully instantiated weapon: ", weapon_name_str)

	# Validate the weapon has proper animation setup
	if weapon_instance.has_node("AnimationPlayer"):
		var anim_player = weapon_instance.get_node("AnimationPlayer")
		var library_list = anim_player.get_animation_library_list()
		if library_list == null or library_list.size() == 0:
			print("WARNING: Weapon " + weapon_name_str + " has no animation libraries, animations may not work")

	# Add the weapon to the weapon position or as a child
	if weapon_position:
		weapon_position.add_child(weapon_instance)
	else:
		add_child(weapon_instance)

	return add_weapon(weapon_instance)

func switch_to_weapon(index: int) -> bool:
	if index < 0 or index >= weapons.size() or weapons[index] == null:
		return false

	if index == current_weapon_index or is_switching:
		return false

	is_switching = true

	# Hide current weapon
	if current_weapon_index != -1 and current_weapon_index < weapons.size() and weapons[current_weapon_index] != null:
		weapons[current_weapon_index].unequip()

	current_weapon_index = index

	# Wait for switch time
	var timer = Timer.new()
	timer.process_mode = Node.PROCESS_MODE_ALWAYS # Make sure it runs even when paused
	timer.wait_time = switch_time
	timer.one_shot = true
	add_child(timer)
	timer.start()
	await timer.timeout
	timer.queue_free()

	# Show new weapon
	weapons[current_weapon_index].equip()

	is_switching = false

	# Emit signal
	emit_signal("weapon_switched", weapons[current_weapon_index])

	return true

func next_weapon() -> bool:
	var next_index = find_next_weapon(current_weapon_index)
	if next_index != -1 and next_index != current_weapon_index:
		switch_to_weapon(next_index)  # Fire and forget
		return true
	return false

func previous_weapon() -> bool:
	var prev_index = find_previous_weapon(current_weapon_index)
	if prev_index != -1 and prev_index != current_weapon_index:
		switch_to_weapon(prev_index)  # Fire and forget
		return true
	return false

func find_next_weapon(current_index: int) -> int:
	if weapons.size() == 0:
		return -1

	var index = (current_index + 1) % weapons.size()
	var start_index = index

	while weapons[index] == null:
		index = (index + 1) % weapons.size()
		if index == start_index:
			return -1  # No weapons available

	return index

func find_previous_weapon(current_index: int) -> int:
	if weapons.size() == 0:
		return -1

	var index = (current_index - 1 + weapons.size()) % weapons.size()
	var start_index = index

	while weapons[index] == null:
		index = (index - 1 + weapons.size()) % weapons.size()
		if index == start_index:
			return -1  # No weapons available

	return index

func primary_fire() -> bool:
	if current_weapon_index == -1 or is_switching:
		return false

	return weapons[current_weapon_index].primary_fire()

func secondary_fire() -> bool:
	if current_weapon_index == -1 or is_switching:
		return false

	return weapons[current_weapon_index].secondary_fire()

func reload_weapon() -> bool:
	if current_weapon_index == -1 or is_switching:
		return false

	weapons[current_weapon_index].reload()
	return true

func get_current_weapon():
	if current_weapon_index == -1:
		return null

	return weapons[current_weapon_index]

func _on_weapon_fired(weapon):
	emit_signal("weapon_fired", weapon)

func _on_ammo_changed(current, reserve):
	if current_weapon_index != -1:
		# Pass the current weapon, current ammo count, and reserve ammo count
		emit_signal("ammo_changed", weapons[current_weapon_index], current, reserve)

func _on_no_ammo(weapon):
	emit_signal("no_ammo", weapon)
	# Optionally auto-switch to next weapon with ammo
	next_weapon_with_ammo()

func next_weapon_with_ammo() -> bool:
	if weapons.size() == 0:
		return false

	var index = (current_weapon_index + 1) % weapons.size()
	var start_index = index

	while index != start_index:
		if weapons[index] != null and weapons[index].has_ammo():
			switch_to_weapon(index)  # Fire and forget
			return true

		index = (index + 1) % weapons.size()
		if index == start_index:
			break

	return false

func add_ammo_to_weapon_type(weapon_type: String, amount: int) -> int:
	var added = 0

	# If we don't have this weapon type yet, create a default weapon of this type
	var has_weapon_type = false
	for weapon in weapons:
		if weapon != null and "weapon_name" in weapon and weapon.weapon_name.to_lower() == weapon_type.to_lower():
			has_weapon_type = true
			break

	if !has_weapon_type:
		print("No weapon of type ", weapon_type, " found. Cannot add ammo without weapon.")
		return 0

	for weapon in weapons:
		if weapon != null and "weapon_name" in weapon and weapon.weapon_name.to_lower() == weapon_type.to_lower():
			added += weapon.add_ammo(amount - added)
			if added >= amount:
				break

	return added

func _handle_pp8_pickup(weapon) -> bool:
	"""Handle PP8 pickup with dual-wield logic"""
	print("WeaponManager: Handling PP8 pickup")

	# Validate weapon object
	if weapon == null or not "weapon_name" in weapon:
		print("ERROR: Invalid PP8 weapon object")
		return false

	# Check if we already have a PP8
	for i in range(weapons.size()):
		if weapons[i] != null and "weapon_name" in weapons[i] and weapons[i].weapon_name == "PP8":
			var existing_pp8 = weapons[i]
			if existing_pp8 and existing_pp8.has_method("setup_dual_wield"):
				if not existing_pp8.is_dual_wielding:
					# First PP8 pickup - setup dual wield
					print("WeaponManager: Setting up PP8 dual-wield")
					_setup_pp8_dual_wield(existing_pp8, weapon)
					weapon.queue_free()
					return true
				else:
					# Already dual wielding - just add ammo
					print("WeaponManager: Adding ammo to dual-wield PP8")
					existing_pp8.add_ammo(weapon.reserve_ammo + weapon.current_ammo)
					emit_signal("ammo_changed", existing_pp8, existing_pp8.current_ammo, existing_pp8.reserve_ammo)
					weapon.queue_free()
					return true

	# No existing PP8 - add as single weapon
	print("WeaponManager: Adding first PP8")

	# Find an empty slot
	var slot = -1
	for i in range(weapons.size()):
		if weapons[i] == null:
			slot = i
			break

	if slot == -1:
		return false  # No empty slots

	# Add weapon to slot
	weapons[slot] = weapon
	weapon.visible = false

	# Connect signals
	weapon.weapon_fired.connect(_on_weapon_fired)
	weapon.ammo_changed.connect(_on_ammo_changed)
	weapon.no_ammo.connect(_on_no_ammo)

	emit_signal("weapon_added", weapon, slot)

	# Auto-switch to new weapon if we don't have one equipped
	if current_weapon_index == -1:
		switch_to_weapon(slot)  # Fire and forget

	return true

func _setup_pp8_dual_wield(right_hand_pp8: PP8Weapon, pickup_weapon: PP8Weapon):
	"""Setup dual-wield PP8 with proper left/right positioning"""
	print("WeaponManager: Creating dual-wield PP8 setup")

	# Create left-hand PP8 as a duplicate of the pickup
	var left_hand_pp8 = pickup_weapon.duplicate()

	# Position the guns properly
	right_hand_pp8.position = Vector3(0.3, -0.2, 0)  # Right side
	right_hand_pp8.rotation_degrees = Vector3(0, -5, 0)  # Angle slightly inward

	left_hand_pp8.position = Vector3(-0.5, -0.2, 0)  # Further left
	left_hand_pp8.rotation_degrees = Vector3(0, 5, 0)  # Angle slightly inward
	left_hand_pp8.scale.x = -1  # Mirror the left gun

	# Set up dual-wield properties
	right_hand_pp8.is_dual_wielding = true
	right_hand_pp8.is_left_hand = false
	right_hand_pp8.dual_wield_partner = left_hand_pp8

	left_hand_pp8.is_dual_wielding = true
	left_hand_pp8.is_left_hand = true
	left_hand_pp8.dual_wield_partner = right_hand_pp8

	# Initialize separate clips and shared reserve
	var total_ammo = right_hand_pp8.current_ammo + right_hand_pp8.reserve_ammo + pickup_weapon.current_ammo + pickup_weapon.reserve_ammo
	var max_total = right_hand_pp8.max_ammo * 2  # Double max for dual wield

	# Don't exceed max capacity
	total_ammo = min(total_ammo, max_total)

	# Set up separate clips - fill both clips first
	right_hand_pp8.right_clip_ammo = min(right_hand_pp8.ammo_per_clip, total_ammo)
	var remaining_after_right = max(0, total_ammo - right_hand_pp8.right_clip_ammo)
	left_hand_pp8.left_clip_ammo = min(left_hand_pp8.ammo_per_clip, remaining_after_right)

	# Remaining ammo goes to reserve
	var used_ammo = right_hand_pp8.right_clip_ammo + left_hand_pp8.left_clip_ammo
	right_hand_pp8.dual_wield_ammo = max(0, total_ammo - used_ammo)
	left_hand_pp8.dual_wield_ammo = right_hand_pp8.dual_wield_ammo

	# Sync clip values between guns
	right_hand_pp8.left_clip_ammo = left_hand_pp8.left_clip_ammo
	left_hand_pp8.right_clip_ammo = right_hand_pp8.right_clip_ammo

	# Set display values
	right_hand_pp8.current_ammo = right_hand_pp8.right_clip_ammo
	right_hand_pp8.reserve_ammo = right_hand_pp8.dual_wield_ammo

	left_hand_pp8.current_ammo = left_hand_pp8.left_clip_ammo
	left_hand_pp8.reserve_ammo = left_hand_pp8.dual_wield_ammo

	# Add left-hand gun to weapon position
	if weapon_position:
		weapon_position.add_child(left_hand_pp8)
	else:
		add_child(left_hand_pp8)

	left_hand_pp8.visible = right_hand_pp8.visible  # Match visibility

	# Connect signals for left gun (but only right gun handles firing logic)
	left_hand_pp8.weapon_fired.connect(_on_weapon_fired)
	left_hand_pp8.ammo_changed.connect(_on_ammo_changed)
	left_hand_pp8.no_ammo.connect(_on_no_ammo)

	# Call setup on both guns
	right_hand_pp8.setup_dual_wield()
	left_hand_pp8.setup_dual_wield()

	emit_signal("ammo_changed", right_hand_pp8, right_hand_pp8.current_ammo, right_hand_pp8.reserve_ammo)

	print("WeaponManager: Dual-wield PP8 setup complete")
